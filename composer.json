{"name": "codeigniter4/appstarter", "type": "project", "description": "CodeIgniter4 starter app", "homepage": "https://codeigniter.com", "license": "MIT", "require": {"php": "^7.3 || ^8.0", "codeigniter4/framework": "^4", "twbs/bootstrap": "5.1.3", "codenix-sv/coingecko-api": "^1.0"}, "require-dev": {"fakerphp/faker": "^1.9", "mikey179/vfsstream": "^1.6", "phpunit/phpunit": "^9.1"}, "suggest": {"ext-fileinfo": "Improves mime type detection for files"}, "autoload": {"psr-4": {"App\\": "app", "Config\\": "app/Config"}, "exclude-from-classmap": ["**/Database/Migrations/**"]}, "autoload-dev": {"psr-4": {"Tests\\Support\\": "tests/_support"}}, "scripts": {"test": "phpunit"}, "support": {"forum": "http://forum.codeigniter.com/", "source": "https://github.com/codeigniter4/CodeIgniter4", "slack": "https://codeigniterchat.slack.com"}}