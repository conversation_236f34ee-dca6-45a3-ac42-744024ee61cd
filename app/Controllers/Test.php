<?php

namespace App\Controllers;

use App\Models\ContiModel;
use App\Models\CryptoModel;
use Codenixsv\CoinGeckoApi\CoinGeckoClient;


class Test extends BaseController
{
    public function test()
    {
        try {
            $crypto = new CryptoModel();
            $client = $crypto->createCoinGeckoClient();
            $data = $client->coins()->getList();
            var_dump($data);

            echo $crypto->getPrice('terra-luna', '31-12-2021');
        } catch (\Exception $e) {
            echo "Errore: " . $e->getMessage();
        }
    }

    public function getSaldo()
    {
        $data='2021-12-31';

        $modelcoins=new ContiModel();
        $modelprezzi=new CryptoModel();

        $coinsfinale=$modelcoins->saldo('chris', $data);

        foreach ($coinsfinale as $coin) {
            $valore=$modelprezzi->getPrice($coin->idCoin, '31-12-2021') * $coin->qta;
            echo "$coin->idCoin : $coin->qta ($valore USD)<br>";
        }
    }
}
