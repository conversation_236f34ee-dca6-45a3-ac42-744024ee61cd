<?php

namespace App\Controllers;

use App\Models\CryptoModel;

class TestRateLimit extends BaseController
{
    /**
     * Test del sistema di rate limiting
     */
    public function index()
    {
        echo "<h1>Test Rate Limiting CoinGecko</h1>";
        
        $cryptoModel = new CryptoModel();
        
        echo "<h2>Stato iniziale:</h2>";
        $this->printStatus();
        
        echo "<h2>Test con richieste multiple:</h2>";
        
        // Simula molte richieste per testare il rate limiting
        $testCoins = ['bitcoin', 'ethereum', 'cardano', 'polkadot', 'chainlink'];
        $testDate = '2023-01-01';
        
        foreach ($testCoins as $coin) {
            try {
                echo "<p>Tentativo richiesta per $coin...</p>";
                $price = $cryptoModel->getMediaPrice($coin, $testDate);
                echo "<p style='color: green;'>✓ Successo: $coin = $price USD</p>";
            } catch (\Exception $e) {
                echo "<p style='color: red;'>✗ Errore: " . $e->getMessage() . "</p>";
            }
            
            // Mostra lo stato dopo ogni richiesta
            $this->printStatus();
            echo "<hr>";
        }
        
        echo "<h2>Stato finale:</h2>";
        $this->printStatus();
        
        echo "<p><a href='" . base_url('RateLimitStatus') . "'>Vai al pannello di controllo</a></p>";
    }
    
    private function printStatus()
    {
        $status = CryptoModel::getRateLimitStatus();
        $pendingRequests = CryptoModel::getPendingRequests();
        
        echo "<div style='background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "<strong>Stato Rate Limiting:</strong><br>";
        echo "Richieste effettuate: {$status['requests_made']}/{$status['max_requests']}<br>";
        echo "Rate limit raggiunto: " . ($status['rate_limit_reached'] ? 'SÌ' : 'NO') . "<br>";
        echo "Richieste pendenti: " . count($pendingRequests) . "<br>";
        
        if ($status['reset_time']) {
            echo "Reset alle: " . date('H:i:s', $status['reset_time']) . "<br>";
        }
        
        if (!empty($pendingRequests)) {
            echo "<strong>Richieste in coda:</strong><br>";
            foreach ($pendingRequests as $req) {
                echo "- {$req['coin']} ({$req['data']}) - {$req['type']}<br>";
            }
        }
        echo "</div>";
    }
    
    /**
     * Reset del sistema per i test
     */
    public function reset()
    {
        // Questo è solo per testing - in produzione non dovrebbe essere disponibile
        echo "<h1>Reset Sistema Rate Limiting</h1>";
        echo "<p>Funzionalità non implementata per sicurezza.</p>";
        echo "<p><a href='" . base_url('TestRateLimit') . "'>Torna al test</a></p>";
    }
}
