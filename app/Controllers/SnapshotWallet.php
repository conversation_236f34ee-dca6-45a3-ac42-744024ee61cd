<?php

namespace App\Controllers;

use App\Models\CoinsModel;
use App\Models\ContiCoinsModel;
use App\Models\ContiModel;
use App\Models\EURUSDModel;
use App\Models\WalletModel;
use Codenixsv\CoinGeckoApi\Api\Coins;

class SnapshotWallet extends BaseController
{
    public function __construct()
    {
        $this->idutente = session()->get('utente')->id;
    }

    public function lista()
    {
        $walletsM = new ContiModel();
        $wallets = $walletsM->getAll($this->idutente);

        echo view('snapshotwallet/listawallet', ['wallets' => $wallets]);
    }

    public function nuovo()
    {
        $data = date('Y-m-d');
        $cm = new ContiModel();
        $conto = $cm->insert(['utente' => $this->idutente, 'data' => $data]);
        $idconto = $cm->getInsertID();
        return redirect()->to(base_url('/snapshotWallet/modificaWallet/' . $idconto));
    }

    /**
     * Lista coin detenute alla data
     *
     * La vista dettaglio, visualizza le coin uguali separate per wallet/luogo
     * @param [type] $idconto
     * @param string $tipo tipo di visualizzazione aggregato|dettaglio
     * @return void
     */
    public function infoWallet($idconto, $tipo = 'aggregato')
    {
        // $wm = new WalletModel();
        $ccm = new ContiCoinsModel();
        $cm = new ContiModel();

        $conto = $cm->find($idconto);
        $saldo = $ccm->saldo($idconto);

        switch ($tipo) {
            case "aggregato":
                $coins = $ccm->getSnapshotAggregato($idconto);
                $switch_vista = 'dettaglio';
                break;
            case "dettaglio":
                $coins = $ccm->getSnapshot($idconto);
                $switch_vista = 'aggregato';
                break;
        }
        $cambioEURUSD = $this->getCambioEURUSD($conto->data);
        $saldoEUR = $saldo / $cambioEURUSD;
        echo view('snapshotwallet/infowallet', ['coins' => $coins, 'conto' => $conto, 'saldo' => $saldo, 'saldoEUR' => $saldoEUR, 'switch_vista' => $switch_vista]);
    }

    public function getCambioEURUSD($data)
    {
        $model = new EURUSDModel();
        $model->where(['data' => $data]);
        $rec = $model->findAll();
        if (count($rec) > 0) {
            $cambio = $rec[0]->usd;
        } else {
            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => "https://api.apilayer.com/exchangerates_data/convert?to=USD&from=EUR&amount=1&date=$data",
                CURLOPT_HTTPHEADER => array(
                    "Content-Type: text/plain",
                    "apikey: B0lKv0ShBbpSX32NJUkcp5J507DlSKk0"
                ),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "GET"
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            $out = json_decode($response);
            $cambio = $out->result;

            $model->insert(['data' => $data, 'usd' => $cambio]);
        }
        return $cambio;
    }

    /**
     * Modifica wallet di una certa data / utente
     *
     * @param int $idconto
     * @return void
     */
    public function modificaWallet($idconto)
    {
        helper('chrisarray');
        $ccm = new ContiCoinsModel();
        $coins = $ccm->getSnapshot($idconto);

        $cm = new ContiModel();
        $conto = $cm->find($idconto);

        $walletM = new WalletModel();
        $walletM->where('idutente', $this->idutente);
        $wallets = $walletM->findAll();
        $wallets = arrayObjToAssoc($wallets, 'id', 'address', [0 => 'Seleziona...'], 5);
        // var_dump($wallets);
        // exit;
        echo view('snapshotwallet/modificawallet', ['coins' => $coins, 'conto' => $conto, 'wallets' => $wallets]);
    }

    public function modificaWallet_ex()
    {
        $coins = $this->request->getPost('coin');
        $qta = $this->request->getPost('qta');
        $luogo = $this->request->getPost('luogo');
        $idconto = $this->request->getPost('idconto');
        $idwallet = $this->request->getPost('idwallet');
        $idconticoin = $this->request->getPost('idconticoin');
        $datasnapshot = $this->request->getPost('datasnapshot');

        $contiM = new ContiModel();
        $contiM->update($idconto, ['data' => $datasnapshot, 'idutente' => $this->idutente]);

        // var_dump($luogo);exit;
        $model = new ContiCoinsModel();
        // $model->where('idconto', $idconto);
        // $model->delete('');
        foreach ($coins as $k => $coin) {
            if ($coin != '') {
                $data = array('idconto' => $idconto, 'idCoin' => $coin, 'qta' => $qta[$k], 'idluogo' => $luogo[$k], 'idwallet' => $idwallet[$k]);
                if (isset($idconticoin[$k])) {
                    if ($qta[$k] != '') $model->update($idconticoin[$k], $data);
                    else {
                        $model->delete($idconticoin[$k]);
                    }
                } else {
                    var_dump($data);
                    $model->insert($data);
                }
            } else {
                echo "$k $coin";
                exit;
            }
        }
        return redirect()->to(base_url('/SnapshotWallet/modificaWallet/' . $idconto));
    }

    public function duplicaWallet($idconto, $data)
    {
        $contiModel = new ContiModel();
        $contiModel->insert(['idutente' => $this->idutente, 'data' => $data]);
        $newid = $contiModel->getInsertID();

        $ccm = new ContiCoinsModel();
        $coins = $ccm->getSnapshot($idconto);
        foreach ($coins as $r) {
            $ccm->insert([
                'idconto' => $newid,
                'idCoin' => $r->idCoin,
                'qta' => $r->qta,
                'idluogo' => $r->idluogo,
                'idwallet' => $r->idwallet
            ]);
        }
        return redirect()->to(base_url('/SnapshotWallet/lista/'));
    }

    public function eliminaWallet($idconto) {
        $cm=new ContiModel();
        $ccm=new ContiCoinsModel();
        $cm->delete($idconto);
        $ccm->where('idconto', $idconto);
        $ccm->delete();
        return redirect()->to(base_url('/SnapshotWallet/lista/'));
    }
}
