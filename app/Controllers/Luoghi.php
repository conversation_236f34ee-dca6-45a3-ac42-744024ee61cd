<?php

namespace App\Controllers;

use App\Models\ChainModel;
use App\Models\LuoghiModel;
use stdClass;

class <PERSON>oghi extends BaseController
{
	public function index()
	{
		$luoghiM = new LuoghiModel();
		$luoghiM->select('luoghi.*, C.chain');
		$luoghiM->join('chain C', 'C.id=idchain', 'left');
		$luoghiM->orderBy('chain');
		$luoghi = $luoghiM->findAll();
		echo view('luoghi/lista', ['luoghi' => $luoghi]);
	}
	public function ajaxCerca($tipoout = 'html')
	{
		$model = new LuoghiModel();

		$output = '';
		if ($tipoout == 'json') $output = array();
		$query = '';

		if ($this->request->getVar('query')) {
			$query = $this->request->getVar('query');
		} elseif ($this->request->getVar('term')) {
			$query = $this->request->getVar('term');
		}
		$model->like('luogo', $query);
		$model->orderBy('luogo', 'asc');
		$data = $model->getAll();
		if (count($data) > 0) {
			if ($tipoout == 'html') $output .= $row->symbol . "<br>";
			elseif ($tipoout == 'option') $output .= '<option>Seleziona...</option>';
			elseif ($tipoout == 'json') $output['results'][] = array('id' => '', 'text' => '---');
			foreach ($data as $row) {
				// var_dump($row); exit;
				if ($tipoout == 'html') $output .= $row->symbol . "<br>";
				elseif ($tipoout == 'option') $output .= '<option>' . $row->symbol . "</option>";
				elseif ($tipoout == 'json') $output['results'][] = array('id' => $row->id, 'text' => "$row->luogo $row->tipo $row->chain");
			}
		}
		if ($tipoout == 'json') $output = json_encode($output);
		echo $output;
	}

	public function nuovo()
	{
		$chainModel = new ChainModel();
		$chains = $chainModel->findAll();

		$chainOptions = [];
		foreach ($chains as $chain) {
			$chainOptions[$chain->id] = $chain->chain;
		}

		$luogo = new stdClass();
		$luogo->id = '';
		$luogo->luogo = '';
		$luogo->tipo = '';
		$luogo->idchain = '';

		echo view('luoghi/modificaluogo', ['luogo' => $luogo, 'chainOptions' => $chainOptions]);
	}


	public function modificaLuogo($id)
	{
		$luoghiM = new LuoghiModel();
		$luoghiM->select('luoghi.*');
		$luogo = $luoghiM->find($id);

		$chainModel = new ChainModel();
		$chains = $chainModel->findAll();

		$chainOptions = [];
		foreach ($chains as $chain) {
			$chainOptions[$chain->id] = $chain->chain;
		}

		echo view('luoghi/modificaluogo', ['luogo' => $luogo, 'chainOptions' => $chainOptions]);
	}

	public function modificaLuogo_ex()
	{
		$data = $this->request->getPost();
		$luoghiM = new LuoghiModel();
		if (isset($data['id'])) {
			$luoghiM->update($data['id'], $data);
		} elseif (isset($data['luogo'])) {
			$luoghiM->insert($data);
		}
		return redirect()->to(base_url('Luoghi'));
	}
	public function aggiungiLuogo()
	{
		$data = $this->request->getPost();
		$luoghiM = new LuoghiModel();
		if (isset($data['luogo'])) {
			$luoghiM->insert($data);
		}
		return redirect()->to(base_url('Luoghi'));
	}
}
