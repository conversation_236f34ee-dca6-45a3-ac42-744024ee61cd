<?php

namespace App\Controllers;

use App\Models\CoinsModel;
use App\Models\CoinsValoriModel;
use App\Models\ContiModel;
use Codenixsv\CoinGeckoApi\CoinGeckoClient;
use App\Models\CryptoModel;
use App\Models\WalletModel;

class Crypto extends BaseController
{
    public function index()
    {
        // Usa il CryptoModel per creare il client con API key
        $cryptoModel = new CryptoModel();

        try {
            $connectivity = $cryptoModel->checkApiConnectivity();
            echo "Stato API: " . $connectivity['status'] . "<br>";
            echo "Messaggio: " . $connectivity['message'] . "<br>";

            if ($connectivity['status'] === 'success') {
                $price = $cryptoModel->getPrice('bitcoin', '31-12-2022');
                echo "Prezzo Bitcoin: $price USD";
            }
        } catch (\Exception $e) {
            echo "Errore: " . $e->getMessage();
        }
    }

    public function ajaxCerca($tipoout = 'html')
    {
        $model = new CoinsModel();

        $output = '';
        if ($tipoout == 'json') $output = array();
        $query = '';

        if ($this->request->getVar('query')) {
            $query = $this->request->getVar('query');
        } elseif ($this->request->getVar('term')) {
            $query = $this->request->getVar('term');
        }
        $model->like('symbol', $query, 'after');
        $data = $model->findAll(25);
        if (count($data) > 0) {
            if ($tipoout == 'html') $output .= $row->symbol . "<br>";
            elseif ($tipoout == 'option') $output .= '<option>Seleziona...</option>';
            elseif ($tipoout == 'json') $output['results'][] = array('id' => '', 'text' => '---');
            foreach ($data as $row) {
                // var_dump($row); exit;
                if ($tipoout == 'html') $output .= $row->symbol . "<br>";
                elseif ($tipoout == 'option') $output .= '<option>' . $row->symbol . "</option>";
                elseif ($tipoout == 'json') $output['results'][] = array('id' => $row->id, 'text' => "$row->symbol ($row->id)");
            }
        }
        if ($tipoout == 'json') $output = json_encode($output);
        echo $output;
    }
    /**
     * Scarica la lista delle coin da CoinGecko
     *
     * @return void
     */
    public function CoinGeckoCoins()
    {
        try {
            $cryptoModel = new CryptoModel();
            $client = $cryptoModel->createCoinGeckoClient();
            $data = $client->coins()->getList();

            $model = new CoinsModel();
            $model->truncate();
            $model->insertBatch($data);

            session()->setFlashdata('msg', 'Lista coins aggiornata con successo!');
            session()->setFlashdata('msgtype', 'success');

        } catch (\Exception $e) {
            session()->setFlashdata('err', 'Errore durante l\'aggiornamento: ' . $e->getMessage());
        }

        return redirect()->to(base_url('Utenti'));
    }

    public function infoWallet($idconto)
    {
        $wm = new WalletModel();
        $coins = $wm->getWallet($idconto);
        $coinsaggregato = $wm->getWalletAggregato($idconto); // non va come dovrebbe...
        $cm = new ContiModel();
        $conto = $cm->find($idconto);
        $saldo = $wm->saldo($idconto);
        echo view('wallet/infowallet', ['coins' => $coinsaggregato, 'conto' => $conto, 'saldo' => $saldo]);
        echo view('wallet/infowallet', ['coins' => $coins, 'conto' => $conto, 'saldo' => $saldo]);
    }
    /**
     * Modifica wallet di una certa data / utente
     *
     * @param int $idconto
     * @return void
     */
    public function modificaWallet($idconto)
    {
        $wm = new WalletModel();
        $coins = $wm->getWallet($idconto);
        // var_dump($coins);exit;

        $cm = new ContiModel();
        $conto = $cm->find($idconto);
        // var_dump($wallet);exit;
        echo view('wallet/modificawallet', ['coins' => $coins, 'conto' => $conto]);
    }



    public function salvaWallet()
    {
        $coins = $this->request->getPost('coin');
        $qta = $this->request->getPost('qta');
        $luogo = $this->request->getPost('luogo');
        $idconto = $this->request->getPost('idconto');
        // var_dump($luogo);exit;
        $model = new WalletModel();
        // $model->where('idconto', $idconto);
        // $model->delete('');
        foreach ($coins as $k => $coin) {
            if ($coin != '') {
                echo $coin . " $idconto " . $qta[$k] . " <br>";
                $model->where(['idconto' => $idconto, 'idCoin' => $coin, 'idluogo' => $luogo[$k]]);
                $rs = $model->findAll();

                $data = array('idconto' => $idconto, 'idCoin' => $coin, 'qta' => $qta[$k], 'idluogo' => $luogo[$k]);
                if (count($rs) == 1) {
                    // var_dump($data);//exit;
                    if ($qta[$k] != '') $model->update($rs[0]->id, $data);
                    else {
                        $model->where(['idconto' => $idconto, 'idCoin' => $coin, 'idluogo' => $luogo[$k]]);
                        $model->delete();
                    }
                } else $model->insert($data);
            } else {
                echo "$k $coin";
                exit;
            }
        }
        return redirect()->to(base_url('/crypto/modificaWallet/' . $idconto));
    }


    public function formSetPrice($id, $id_conto)
    {
        $model = new CoinsValoriModel();
        $coin = $model->find($id);
        echo view('crypto/form_setprice', ['coin' => $coin, 'id_conto' => $id_conto]);
    }

    public function updatePrice()
    {
        $model = new CoinsValoriModel();
        $data = $this->request->getPost();
        $id = $this->request->getPost('id');
        $usd = $this->request->getPost('usd');
        $id_conto = $this->request->getPost('id_conto');
        $model->update($id, ['usd' => $usd]);
        return redirect()->to(base_url('/SnapshotWallet/infoWallet/' . $id_conto));
    }


    public function getPrice($coin, $data)
    {
        $crypto = new CryptoModel();
        echo "$coin $data " . $crypto->getMediaPrice($coin, $data);
    }
}
