<?php

namespace App\Controllers;

use App\Models\ChainModel;
use App\Models\WalletModel;

class Wallet extends BaseController
{
    public function __construct()
    {
        $this->idutente = session()->get('utente')->id;
    }
    public function lista()
    {
        helper(['form']);
        $walletM = new WalletModel();
        $walletM->where('wallet.idutente', $this->idutente);
        $walletM->orderBy('address');
        $wallets = $walletM->findAll();

        $chainM = new ChainModel();
        $chainopt = $chainM->arrayopzioni();

        echo view('utenti/listawallet', ['wallets' => $wallets]);
    }

    public function nuovo_ex()
    {
        $data = $this->request->getPost();
        $note = $data['note'];
        $address = $data['address'];

        $walletM = new WalletModel();

        $walletM->insert(['address' => $address, 'idutente' => $this->idutente, 'note' => $note]);

        return redirect()->to(base_url('Wallet/lista'));
    }

    public function ajaxCerca($tipoout = 'html')
    {
        $model = new WalletModel();

        $output = '';
        if ($tipoout == 'json') $output = array();
        $query = '';

        if ($this->request->getVar('query')) {
            $query = $this->request->getVar('query');
        } elseif ($this->request->getVar('term')) {
            $query = $this->request->getVar('term');
        }
        $model->like('address', $query);
        $model->orderBy('address', 'asc');
        $data = $model->findAll(50);
        if (count($data) > 0) {
            if ($tipoout == 'html') $output .= $row->address . "<br>";
            elseif ($tipoout == 'option') $output .= '<option>Seleziona...</option>';
            elseif ($tipoout == 'json') $output['results'][] = array('id' => '', 'text' => '---');
            foreach ($data as $row) {
                // var_dump($row); exit;
                if ($tipoout == 'html') $output .= $row->address . "<br>";
                elseif ($tipoout == 'option') $output .= '<option>' . $row->address . "</option>";
                elseif ($tipoout == 'json') $output['results'][] = array('id' => $row->id, 'text' => substr($row->address, 0, 5) . '...' . substr($row->address, -5));
            }
        }
        if ($tipoout == 'json') $output = json_encode($output);
        echo $output;
    }
}
