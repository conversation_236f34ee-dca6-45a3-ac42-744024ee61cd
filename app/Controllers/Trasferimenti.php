<?php

namespace App\Controllers;

use CodeIgniter\I18n\Time;
use App\Models\UtentiModel;
use App\Models\BankModel;
use App\Models\TrasferimentiModel;
use stdClass;

class Trasferimenti extends BaseController
{
    public function __construct()
    {
        $this->idutente = session()->get('utente')->id;
    }
    public function index()
    {
        $data = $this->request->getPost();
        $filtro = '';
        if (isset($data['filtro'])) {
            $filtro = $data['filtro'];
            $bankM = new BankModel();
            $bankM->select('id');
            $bankM->like('nome', $filtro);
            $banche = $bankM->findAll();
            $arrids = array();
            foreach ($banche as $b) {
                $arrids[] = $b->id;
            }
            // $filtrobanche = implode(',', $arrids);
        }
        $model = new TrasferimentiModel();
        $db = db_connect();
        $subq1 = $db->table('bank')->select('nome')->where('bank.id=trasferimenti.idbank_da');
        $subq2 = $db->table('bank')->select('nome')->where('bank.id=trasferimenti.idbank_a');
        $model->select('*, date_format(data, "%d-%m-%Y") as dataita');
        $model->selectSubquery($subq1, 'trasf_da')->selectSubquery($subq2, 'trasf_a');
        $model->where('idutente', $this->idutente);
        if ($filtro != '') {
            $model->groupStart();
            $model->whereIn('idbank_da', $arrids);
            $model->orWhereIn('idbank_a', $arrids);
            $model->groupEnd();
        }
        // echo $model->builder->getCompiledSelect();
        // exit;
        $model->orderBy('data', 'desc');
        $trasf = $model->findAll();
        echo view('trasferimenti/lista', ['trasf' => $trasf, 'filtro' => $filtro]);
    }

    public function modifica($id = '', $clona = false)
    {
        $model = new TrasferimentiModel();
        $bancheM = new BankModel();
        $bancheM->select('id as key, nome as val');
        $banche = $bancheM->asArray();
        $banche = $bancheM->findAll();

        if ($id == '') { // nuovo
            $trasf = new stdClass();
            foreach ($model->allowedFields as $field) {
                $trasf->$field = '';
            }
            $trasf->dataita = '';
            $trasf->id = '';
        } else { // modifica
            $model->select('*, DATE_FORMAT(data, "%d-%m-%Y") as dataita');
            $trasf = $model->find($id);
            if ($clona) {
                $trasf->id = '';
            }
        }

        echo view('trasferimenti/form', [
            'banche' => $banche,
            'metodi' => $model->metodiToAssoc(),
            'trasf' => $trasf,
        ]);
    }

    public function modifica_ex()
    {
        $model = new TrasferimentiModel();
        $data = $this->request->getPost();
        $data['data'] = str_replace('/', '-', $data['data']);
        $time = Time::createFromFormat('d-m-Y', $data['data'], 'America/Chicago');
        $data['data'] = $time->toDateString();
        if ($data['id'] == '') {
            $data['idutente'] = $this->idutente;
            $model->insert($data);
            $this->session->setFlashdata(['msg' => 'Trasferimento aggiunto', 'msgtype' => 'success']);
        } else {
            $model->update($data['id'], $data);
            $this->session->setFlashdata(['msg' => 'Trasferimento aggiornato', 'msgtype' => 'success']);
        }
        return redirect()->to(base_url('Trasferimenti'));
    }

    public function elimina_ex($id)
    {
        if ($id) {
            $model = new TrasferimentiModel();
            $model->delete($id);
            $this->session->setFlashdata(['msg' => 'Eliminato', 'msgtype' => 'success']);
            return redirect()->to(base_url('Trasferimenti'));
        }
    }
}
