<?php

namespace App\Controllers;

use App\Models\UtentiModel;

class Utenti extends BaseController
{
    public function index()
    {
        $utentiM = new UtentiModel();
        $utenti = $utentiM->findAll();
        echo view('utenti/lista', ['utenti' => $utenti]);
    }

    public function setUtente($id)
    {
        $utentiM = new UtentiModel();
        $utente = $utentiM->find($id);
        if ($utente) $this->session->set('utente', $utente);
        return redirect()->to(base_url('Utenti'));
    }
}
