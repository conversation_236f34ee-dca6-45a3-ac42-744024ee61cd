<?php

namespace App\Controllers;

use App\Models\CryptoModel;

class RateLimitStatus extends BaseController
{
    /**
     * Mostra lo stato del rate limiting e le richieste pendenti
     */
    public function index()
    {
        $rateLimitStatus = CryptoModel::getRateLimitStatus();
        $pendingRequests = CryptoModel::getPendingRequests();

        // Test di connettività API (opzionale)
        $apiConnectivity = null;
        if (!$rateLimitStatus['rate_limit_reached']) {
            $cryptoModel = new CryptoModel();
            $apiConnectivity = $cryptoModel->checkApiConnectivity();
        }

        // Formatta le richieste pendenti per la visualizzazione
        $formattedRequests = [];
        foreach ($pendingRequests as $request) {
            $formattedRequests[] = [
                'coin' => $request['coin'],
                'data' => $request['data'],
                'type' => $request['type'] === 'price' ? 'Prezzo singolo' : 'Prezzo medio',
                'timestamp' => date('H:i:s d/m/Y', $request['timestamp'])
            ];
        }

        $data = [
            'rate_limit_status' => $rateLimitStatus,
            'pending_requests' => $formattedRequests,
            'api_connectivity' => $apiConnectivity
        ];

        echo view('ratelimit/status', $data);
    }
    
    /**
     * API endpoint per ottenere lo stato in formato JSON
     */
    public function apiStatus()
    {
        $rateLimitStatus = CryptoModel::getRateLimitStatus();
        $pendingRequests = CryptoModel::getPendingRequests();
        
        $response = [
            'rate_limit_status' => $rateLimitStatus,
            'pending_requests_count' => count($pendingRequests),
            'pending_requests' => $pendingRequests
        ];
        
        return $this->response->setJSON($response);
    }
    
    /**
     * Tenta di processare le richieste pendenti
     */
    public function processPendingRequests()
    {
        $cryptoModel = new CryptoModel();
        $results = $cryptoModel->processPendingRequests();

        if ($results['processed'] > 0) {
            session()->setFlashdata('msg', "Processate {$results['processed']} richieste. Rimanenti: {$results['remaining']}");
            session()->setFlashdata('msgtype', 'success');
        } elseif ($results['failed'] > 0) {
            session()->setFlashdata('err', "Errori nel processamento: " . implode(', ', $results['errors']));
        } else {
            session()->setFlashdata('msg', 'Nessuna richiesta da processare o rate limit ancora attivo.');
            session()->setFlashdata('msgtype', 'info');
        }

        return redirect()->to(base_url('RateLimitStatus'));
    }

    /**
     * Pulisce le richieste pendenti più vecchie
     */
    public function cleanOldRequests()
    {
        CryptoModel::cleanOldPendingRequests();

        session()->setFlashdata('msg', 'Richieste pendenti vecchie pulite.');
        session()->setFlashdata('msgtype', 'info');

        return redirect()->to(base_url('RateLimitStatus'));
    }

    /**
     * Mostra informazioni sulla configurazione API
     */
    public function config()
    {
        $config = config('CoinGecko');
        $apiKey = $config->apiKey ?? env('COINGECKO_API_KEY', null);

        $configInfo = [
            'api_key_configured' => !empty($apiKey) && $apiKey !== 'TUA_API_KEY_QUI',
            'api_key_length' => $apiKey ? strlen($apiKey) : 0,
            'api_key_preview' => $apiKey ? substr($apiKey, 0, 8) . '...' : 'Non configurata',
            'base_url' => $config->baseUrl ?? 'https://api.coingecko.com',
            'max_requests' => $config->maxRequestsPerMinute ?? 30,
            'timeout' => $config->timeout ?? 30
        ];

        // Test di connettività se l'API key è configurata
        $connectivityTest = null;
        if ($configInfo['api_key_configured']) {
            $cryptoModel = new CryptoModel();
            $connectivityTest = $cryptoModel->checkApiConnectivity();
        }

        $data = [
            'config_info' => $configInfo,
            'connectivity_test' => $connectivityTest
        ];

        echo view('ratelimit/config', $data);
    }
}
