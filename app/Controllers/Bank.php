<?php

namespace App\Controllers;

use App\Models\BankModel;
use App\Models\TrasferimentiModel;

class Bank extends BaseController
{
    public function index()
    {
        $bancheM = new BankModel();
        $banche = $bancheM->findAll();
        echo view('bank/lista', ['banche' => $banche, 'tipi' => $bancheM->tipiToAssoc()]);
    }

    public function modifica($id)
    {
        $bancheM = new BankModel();
        $banca = $bancheM->find($id);
        echo view('bank/form', ['banca' => $banca, 'tipi' => $bancheM->tipiToAssoc()]);
    }

    public function modifica_ex()
    {
        $bankM = new BankModel();
        $data = $this->request->getPost();
        $banca = $data['nome'];
        if ($data['id'] == '') {
            $bankM->insert(['nome' => $banca, 'tipo' => $data['tipo']]);
        } else {
            $bankM->update($data['id'], ['nome' => $banca, 'tipo' => $data['tipo']]);
        }

        return redirect()->to(base_url('Bank'));
    }

    public function elimina_ex($id)
    {
        if ($id) {
            $model = new BankModel();
            $trasfM = new TrasferimentiModel();
            $trasfM->select('count(*) as num');
            $trasfM->where('idbank_da', $id);
            $trasfM->orWhere('idbank_a', $id);
            $trasf = $trasfM->findAll();
            if ($trasf[0]->num > 0) {
                $this->session->setFlashdata(['msg' => 'Questa banca contiene trasferimenti', 'msgtype' => 'danger']);
            } else {
                $model->delete($id);
                $this->session->setFlashdata(['msg' => 'Eliminato', 'msgtype' => 'success']);
            }
            return redirect()->to(base_url('Bank/'));
        }
    }
}
