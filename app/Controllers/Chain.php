<?php

namespace App\Controllers;

use App\Models\ChainModel;

class Chain extends BaseController
{
    public function index()
    {
        $chainM = new ChainModel();
        $chainM->orderBy('chain', 'asc');
        $chains = $chainM->findAll();
        echo view('chain/lista', ['chains' => $chains]);
    }

    public function nuovo_ex()
    {
        $data = $this->request->getPost();
        $chain = $data['chain'];

        $chainM = new ChainModel();
        $chainM->insert(['chain' => $chain]);

        return redirect()->to(base_url('Chain'));
    }

    public function ajaxCerca($tipoout = 'html')
    {
        $model = new ChainModel();

        $output = '';
        if ($tipoout == 'json') $output = array();
        $query = '';

        if ($this->request->getVar('query')) {
            $query = $this->request->getVar('query');
        } elseif ($this->request->getVar('term')) {
            $query = $this->request->getVar('term');
        }
        $model->like('chain', $query);
        $model->orderBy('chain', 'asc');
        $data = $model->findAll(50);
        if (count($data) > 0) {
            if ($tipoout == 'html') $output .= $row->chain . "<br>";
            elseif ($tipoout == 'option') $output .= '<option>Seleziona...</option>';
            elseif ($tipoout == 'json') $output['results'][] = array('id' => '', 'text' => '---');
            foreach ($data as $row) {
                // var_dump($row); exit;
                if ($tipoout == 'html') $output .= $row->chain . "<br>";
                elseif ($tipoout == 'option') $output .= '<option>' . $row->chain . "</option>";
                elseif ($tipoout == 'json') $output['results'][] = array('id' => $row->id, 'text' => $row->chain);
            }
        }
        if ($tipoout == 'json') $output = json_encode($output);
        echo $output;
    }

    public function modificaChain($id)
    {
        $chainM = new ChainModel();
        $chain = $chainM->find($id);
        echo view('chain/modificachain', ['chain' => $chain]);
    }

    public function modificaChain_ex()
    {
        $data = $this->request->getPost();
        if (isset($data['id'])) {
            $chainM = new ChainModel();
            $chainM->update($data['id'], $data);
        }
        return redirect()->to(base_url('Chain'));
    }
}
