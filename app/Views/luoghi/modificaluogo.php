<?= $this->extend('layout') ?>
<?= $this->section('content') ?>
<?php helper('form'); ?>

<?php
echo form_open(base_url('Luoghi/modificaLuogo_ex'));
?>
    <div class="row">
        <div class="col">
            <?php
            echo form_input('luogo', $luogo->luogo, 'class="form-control"');
            ?>
        </div>
        <div class="col">
            <?php
            echo form_dropdown('idchain', $chainOptions, $luogo->idchain, 'class="select2"');
            ?>
        </div>
    </div>
<?php
echo form_hidden('id', $luogo->id);
echo form_submit('salva', 'salva', 'class="btn btn-primary"');
echo form_close();
?>
<?= $this->endSection() ?>

<?= $this->section('javascript') ?>
<script>
    $(function() {
        $('.select2').select2({
            width: '100%',
            theme: "bootstrap4",
            language: "it",
            // minimumInputLength: 2,
            ajax: {
                url: '<?= base_url('/Chain/ajaxCerca/json') ?>',
                dataType: 'json',
                delay: 200
            }
        });
    });
</script>
<?= $this->endSection() ?>
