<?= $this->extend('layout') ?>
<?= $this->section('content') ?>
<a href="<?= base_url('Trasferimenti/modifica/') ?>">Nuovo trasferimento</a>
<?php
echo form_open('', 'class="form-inline"');
echo form_input('filtro', $filtro, 'class="form-control"');
echo form_submit('submit', 'filtra', 'class="btn btn-primary btn-sm"');
echo form_close();
?>
<table class="table">
    <?php
    $totaledepositi = 0;
    $totaleprelievi = 0;
    foreach ($trasf as $t) :
        if ($t->qta > 0) $totaledepositi += $t->qta;
        else $totaleprelievi += $t->qta;
    ?>
        <tr>
            <td><?= $t->dataita ?></td>
            <td><?= $t->trasf_da . " -> " . $t->trasf_a . ' (' . $t->metodo, ')' ?></td>
            <td><?= $t->qta . $t->divisa ?></td>
            <td>
                <a href="<?= base_url('Trasferimenti/modifica/' . $t->id) ?>"><i class="fa-solid fa-pen-to-square"></i></a>
                <a onclick="javascript:return confirm('Elimino?')" href="<?= base_url('Trasferimenti/elimina_ex/' . $t->id) ?>"><i class="fa-solid fa-trash"></i></a>
                <a href="<?= base_url('Trasferimenti/modifica/' . $t->id . '/clona') ?>"><i class="fa-solid fa-clone"></i></a>
            </td>
        </tr>
    <?php endforeach ?>
</table>
Totale depositi: <?= $totaledepositi ?><br>
Totale prelievi: <?= -$totaleprelievi ?>
<?= $this->endSection() ?>