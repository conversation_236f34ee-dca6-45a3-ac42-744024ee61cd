<?= $this->extend('layout') ?>
<?= $this->section('content') ?>
<table class="table">
    <?php
    foreach ($banche as $banca) :
    ?>
        <tr>
            <td><?= $banca->nome ?></td>
            <td><?= $banca->tipo ?></td>
            <td>
                <a href="<?= base_url('Bank/modifica/' . $banca->id) ?>"><i class="fa-solid fa-pen-to-square"></i></a>
                <a onclick="javascript:return confirm('Elimino?')" href="<?= base_url('Bank/elimina_ex/' . $banca->id) ?>"><i class="fa-solid fa-trash"></i></a>
            </td>
        </tr>
    <?php endforeach ?>
</table>
<?php
echo form_open(base_url('Bank/modifica_ex'), 'class="form-inline"');
echo form_input('nome', '', 'class="form-control"');
echo chrishtml_dropdown('Tipo', 'tipo', $tipi, '');
echo form_submit('Aggiungi', 'Aggiungi', 'class="btn btn-primary"');
echo form_hidden('id', '');
echo form_close();
?>
<?= $this->endSection() ?>