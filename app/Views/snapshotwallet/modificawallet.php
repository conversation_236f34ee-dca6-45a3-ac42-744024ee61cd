<?= $this->extend('layout') ?>
<?= $this->section('content') ?>
<?php helper('form'); ?>

<h1>Wallet <?= $conto->data ?></h1>
<p>
    <a href="<?= base_url('SnapshotWallet/lista') ?>">Lista</a> <a href="<?= base_url("SnapshotWallet/infoWallet/$conto->id") ?>">Info</a>
</p>
<?php echo form_open(base_url('SnapshotWallet/modificaWallet_ex')); ?>
<table class="table">
    <tr>
        <th>Coin</th>
        <th>Quantità</th>
        <th>Luogo</th>
        <th>Address</th>
    </tr>
    <?php
    foreach ($coins as $coin) :
    ?>
        <tr>
            <td>
                <?php
                echo form_hidden('idconticoin[]', $coin->id);
                echo form_dropdown('coin[]', [$coin->idCoin => "$coin->symbol ($coin->idCoin)"], '', 'class="select2"');
                ?>
            </td>
            <td>
                <?php
                echo form_input('qta[]', $coin->qta, 'class="form-control"');
                ?>
            </td>
            <td>
                <?php
                echo form_dropdown('luogo[]', [$coin->idluogo => "$coin->luogo $coin->tipo $coin->chain"], '', 'class="select2-luoghi"');
                ?>
            </td>
            <td>
                <?php
                echo form_dropdown('idwallet[]', $wallets, $coin->idwallet, 'class="select2-idwallet"');
                ?>
            </td>
        </tr>
    <?php
    endforeach;
    ?>
</table>
<hr>
<h3>Aggiungi coin</h3>
<?php for ($i = 0; $i < 4; $i++) : ?>
    <div class="row addcoin">
        <div class="col">
            <?php
            echo form_dropdown('coin[]', [], '', 'class="select2"');
            ?>
        </div>
        <div class="col">
            <?php
            echo form_input('qta[]', '', 'class="form-control"');
            ?>
        </div>
        <div class="col">
            <?php
            echo form_dropdown('luogo[]', '', '', 'class="select2-luoghi"');
            ?>
        </div>
        <div class="col">
            <?php
            echo form_dropdown('idwallet[]', $wallets, '', 'class="select2-idwallet"');
            ?>
        </div>
    </div>
<?php endfor ?>
<?php
echo 'Modifica data snapshot ' . form_input('datasnapshot', $conto->data, 'class="form-control"');
echo form_hidden('idconto', $conto->id);
echo form_submit('salva', 'salva', 'class="btn btn-primary"');
echo form_close();
?>
<?= $this->endSection() ?>

<?= $this->section('javascript') ?>
<script>
    $(function() {
        $('.select2').select2({
            width: '100%',
            theme: "bootstrap4",
            language: "it",
            // minimumInputLength: 2,
            ajax: {
                url: '<?= base_url('/Crypto/ajaxCerca/json') ?>',
                dataType: 'json',
                delay: 200
            }
        });
        $('.select2-luoghi').select2({
            width: '100%',
            theme: "bootstrap4",
            language: "it",
            // minimumInputLength: 2,
            ajax: {
                url: '<?= base_url('/Luoghi/ajaxCerca/json') ?>',
                dataType: 'json',
                delay: 200
            }
        });

        $('.select2-idwallet').select2({
            width: '100%',
            theme: "bootstrap4",
            language: "it",
            // minimumInputLength: 2,
            ajax: {
                url: '<?= base_url('/Wallet/ajaxCerca/json') ?>',
                dataType: 'json',
                delay: 200
            }
        });
    });
</script>
<?= $this->endSection() ?>