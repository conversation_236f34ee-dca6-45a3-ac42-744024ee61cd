<?= $this->extend('layout') ?>
<?= $this->section('content') ?>
<h1>Wallet <?= $conto->data ?></h1>
<p>
    <a href="<?= base_url('SnapshotWallet/lista') ?>">Lista</a>
    <a href="<?= base_url("SnapshotWallet/modificaWallet/$conto->id") ?>">Modifica</a>
    <a href="<?= base_url("SnapshotWallet/infoWallet/$conto->id/$switch_vista") ?>">Visualizza <?= $switch_vista ?></a>
</p>
<table class="table table-sm small">
    <tr>
        <th>Coin</th>
        <th>Quantità</th>
        <th>Valore</th>
        <th>Luogo</th>
        <th>Chain</th>
        <th>Prezzo coin</th>
    </tr>
    <?php
    foreach ($coins as $coin) :
    ?>
        <tr>
            <td>
                <?= "$coin->symbol ($coin->idCoin)" ?>
            </td>
            <td>
                <?= $coin->qta ?>
            </td>
            <td>
                <?= $coin->valore ?>
            </td>
            <td>
                <?= $coin->luogo ?>
            </td>
            <td>
                <?= $coin->chain ?>
            </td>
            <td>
                <a href="<?= base_url("Crypto/formSetPrice/$coin->id_coin_valore/$conto->id") ?>" title="imposta prezzo manuale"><?= $coin->coinprice ?></a>
            </td>
        </tr>
    <?php
    endforeach;
    ?>
</table>
<h4>Saldo: <?= $saldo ?> USD / <?= $saldoEUR ?> EUR</h4>
<?= $this->endSection() ?>