<?= $this->extend('layout') ?>
<?= $this->section('content') ?>
<h1>Wallet <?= $conto->data ?></h1>
<p>
    <a href="<?= base_url('SnapshotWallet/lista') ?>">Lista</a>
    <a href="<?= base_url("SnapshotWallet/modificaWallet/$conto->id") ?>">Modifica</a>
    <a href="<?= base_url("SnapshotWallet/infoWallet/$conto->id/$switch_vista") ?>">Visualizza <?= $switch_vista ?></a>
    <a href="<?= base_url('RateLimitStatus') ?>" class="btn btn-sm btn-outline-info">
        <i class="fas fa-tachometer-alt"></i> Stato API
    </a>
</p>

<?php
// Controlla la configurazione dell'API key
$config = config('CoinGecko');
$apiKey = $config->apiKey ?? env('COINGECKO_API_KEY', null);
$apiKeyConfigured = !empty($apiKey) && $apiKey !== 'TUA_API_KEY_QUI';

// Controlla se ci sono problemi con l'API CoinGecko
$rateLimitStatus = \App\Models\CryptoModel::getRateLimitStatus();
?>

<?php if (!$apiKeyConfigured): ?>
<div class="alert alert-warning alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle"></i>
    <strong>API Key CoinGecko non configurata!</strong>
    Le valutazioni delle coin potrebbero non funzionare correttamente.
    <a href="<?= base_url('RateLimitStatus/config') ?>" class="alert-link">Configura ora</a>
    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>
</div>
<?php endif; ?>

<?php if ($rateLimitStatus['rate_limit_reached'] || $rateLimitStatus['pending_requests'] > 0): ?>
<div class="alert alert-warning alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle"></i>
    <strong>Attenzione:</strong>
    <?php if ($rateLimitStatus['rate_limit_reached']): ?>
        Rate limit CoinGecko raggiunto. Alcune valutazioni potrebbero non essere aggiornate.
    <?php endif; ?>
    <?php if ($rateLimitStatus['pending_requests'] > 0): ?>
        Ci sono <?= $rateLimitStatus['pending_requests'] ?> richieste in coda.
    <?php endif; ?>
    <a href="<?= base_url('RateLimitStatus') ?>" class="alert-link">Visualizza dettagli</a>
    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>
</div>
<?php endif; ?>
<table class="table table-sm small">
    <tr>
        <th>Coin</th>
        <th>Quantità</th>
        <th>Valore</th>
        <th>Luogo</th>
        <th>Chain</th>
        <th>Prezzo coin</th>
    </tr>
    <?php
    foreach ($coins as $coin) :
    ?>
        <tr>
            <td>
                <?= "$coin->symbol ($coin->idCoin)" ?>
            </td>
            <td>
                <?= $coin->qta ?>
            </td>
            <td>
                <?= $coin->valore ?>
            </td>
            <td>
                <?= $coin->luogo ?>
            </td>
            <td>
                <?= $coin->chain ?>
            </td>
            <td>
                <a href="<?= base_url("Crypto/formSetPrice/$coin->id_coin_valore/$conto->id") ?>" title="imposta prezzo manuale"><?= $coin->coinprice ?></a>
            </td>
        </tr>
    <?php
    endforeach;
    ?>
</table>
<h4>Saldo: <?= $saldo ?> USD / <?= $saldoEUR ?> EUR</h4>
<?= $this->endSection() ?>