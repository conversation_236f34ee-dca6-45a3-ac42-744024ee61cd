<?= $this->extend('layout') ?>
<?= $this->section('content') ?>
<table class="table">
    <tr>
        <th>Data snapshot</th>
        <th>Saldo</th>
        <th></th>
    </tr>
    <?php
    foreach ($wallets as $w) :
        $data = new DateTime($w->data);
        $anno = $data->format('Y');
    ?>
        <tr>
            <td><?= $data->format('d-m-Y') ?></td>
            <td><?= $w->saldo ?></td>
            <td><a class="btn btn-primary btn-sm" href="<?= base_url("SnapshotWallet/modificaWallet/$w->id") ?>">Modifica</a>
                <a class="btn btn-primary btn-sm" href="<?= base_url("SnapshotWallet/infoWallet/$w->id") ?>">Info</a>
                <a class="btn btn-primary btn-sm" href="<?= base_url("SnapshotWallet/duplicaWallet/$w->id/" . $anno . '-12-31') ?>">Duplica 31/12</a>
                <a class="btn btn-primary btn-sm" href="<?= base_url("SnapshotWallet/duplicaWallet/$w->id/" . ($anno + 1) . '-01-01') ?>">Duplica 01/01</a>
                <a class="btn btn-primary btn-sm" href="<?= base_url("SnapshotWallet/duplicaWallet/$w->id/" . date('Y-m-d')) ?>">Duplica oggi</a>
                <a class="btn btn-danger btn-sm" href="<?= base_url("SnapshotWallet/eliminaWallet/$w->id") ?>">Elimina</a>
            </td>
        </tr>
    <?php endforeach ?>
</table>
<?= $this->endSection() ?>