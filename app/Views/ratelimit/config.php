<?= $this->extend('layout') ?>
<?= $this->section('content') ?>

<h1>Configurazione API CoinGecko</h1>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Stato Configurazione</h5>
            </div>
            <div class="card-body">
                <?php if ($config_info['api_key_configured']): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <strong>API Key Configurata!</strong>
                    </div>
                <?php else: ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>API Key NON Configurata!</strong>
                        <p class="mb-0 mt-2">Per utilizzare l'API CoinGecko devi configurare una API key.</p>
                    </div>
                <?php endif; ?>
                
                <table class="table table-sm">
                    <tr>
                        <td><strong>API Key:</strong></td>
                        <td>
                            <?php if ($config_info['api_key_configured']): ?>
                                <span class="badge badge-success">
                                    <i class="fas fa-key"></i> <?= $config_info['api_key_preview'] ?>
                                </span>
                                <small class="text-muted">(<?= $config_info['api_key_length'] ?> caratteri)</small>
                            <?php else: ?>
                                <span class="badge badge-danger">
                                    <i class="fas fa-times"></i> Non configurata
                                </span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Base URL:</strong></td>
                        <td><code><?= htmlspecialchars($config_info['base_url']) ?></code></td>
                    </tr>
                    <tr>
                        <td><strong>Rate Limit:</strong></td>
                        <td><?= $config_info['max_requests'] ?> richieste/minuto</td>
                    </tr>
                    <tr>
                        <td><strong>Timeout:</strong></td>
                        <td><?= $config_info['timeout'] ?> secondi</td>
                    </tr>
                </table>
                
                <?php if ($connectivity_test): ?>
                <div class="mt-4">
                    <h6>Test di Connettività</h6>
                    <?php if ($connectivity_test['status'] === 'success'): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check"></i> <?= htmlspecialchars($connectivity_test['message']) ?>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-times"></i> <?= htmlspecialchars($connectivity_test['message']) ?>
                        </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Come Configurare</h5>
            </div>
            <div class="card-body">
                <ol>
                    <li>Vai su <a href="https://www.coingecko.com/en/api/pricing" target="_blank">CoinGecko API</a></li>
                    <li>Crea un account gratuito</li>
                    <li>Ottieni la tua API key</li>
                    <li>Modifica il file <code>.env</code> nella root del progetto</li>
                    <li>Aggiungi la riga:<br>
                        <code>COINGECKO_API_KEY = tua_api_key_qui</code>
                    </li>
                    <li>Ricarica questa pagina per verificare</li>
                </ol>
                
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle"></i>
                    <strong>Nota:</strong> Il piano gratuito di CoinGecko include 30 richieste al minuto.
                </div>
            </div>
        </div>
        
        <?php if (!$config_info['api_key_configured']): ?>
        <div class="card mt-3">
            <div class="card-header bg-warning">
                <h5 class="card-title mb-0">⚠️ Importante</h5>
            </div>
            <div class="card-body">
                <p>Senza una API key configurata, le richieste a CoinGecko falliranno con errore 401 (Unauthorized).</p>
                <p class="mb-0">Configura l'API key per utilizzare tutte le funzionalità dell'applicazione.</p>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<div class="mt-3">
    <a href="<?= base_url('RateLimitStatus') ?>" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Torna allo Stato API
    </a>
    <button onclick="location.reload()" class="btn btn-primary">
        <i class="fas fa-sync-alt"></i> Ricarica Configurazione
    </button>
    <?php if ($config_info['api_key_configured']): ?>
    <a href="<?= base_url('RateLimitStatus/processPendingRequests') ?>" class="btn btn-success">
        <i class="fas fa-play"></i> Test API
    </a>
    <?php endif; ?>
</div>

<?= $this->endSection() ?>
