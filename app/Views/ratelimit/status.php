<?= $this->extend('layout') ?>
<?= $this->section('content') ?>

<h1>Stato Rate Limiting CoinGecko</h1>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Stato Attuale</h5>
            </div>
            <div class="card-body">
                <?php if ($rate_limit_status['rate_limit_reached']): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Rate Limit Raggiunto!</strong>
                    </div>
                <?php else: ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <strong>API Disponibile</strong>
                    </div>
                <?php endif; ?>
                
                <table class="table table-sm">
                    <tr>
                        <td><strong>Richieste effettuate:</strong></td>
                        <td>
                            <span class="badge badge-<?= $rate_limit_status['requests_made'] >= $rate_limit_status['max_requests'] ? 'danger' : 'primary' ?>">
                                <?= $rate_limit_status['requests_made'] ?>/<?= $rate_limit_status['max_requests'] ?>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Richieste pendenti:</strong></td>
                        <td>
                            <span class="badge badge-warning">
                                <?= $rate_limit_status['pending_requests'] ?>
                            </span>
                        </td>
                    </tr>
                    <?php if ($rate_limit_status['reset_time']): ?>
                    <tr>
                        <td><strong>Reset limite alle:</strong></td>
                        <td><?= date('H:i:s', $rate_limit_status['reset_time']) ?></td>
                    </tr>
                    <?php endif; ?>
                </table>
                
                <div class="progress mb-3">
                    <?php 
                    $percentage = ($rate_limit_status['requests_made'] / $rate_limit_status['max_requests']) * 100;
                    $progressClass = $percentage >= 100 ? 'bg-danger' : ($percentage >= 80 ? 'bg-warning' : 'bg-success');
                    ?>
                    <div class="progress-bar <?= $progressClass ?>" role="progressbar" 
                         style="width: <?= min($percentage, 100) ?>%" 
                         aria-valuenow="<?= $rate_limit_status['requests_made'] ?>" 
                         aria-valuemin="0" 
                         aria-valuemax="<?= $rate_limit_status['max_requests'] ?>">
                        <?= round($percentage, 1) ?>%
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Informazioni</h5>
            </div>
            <div class="card-body">
                <p><strong>Limite CoinGecko:</strong> 30 richieste al minuto</p>
                <p><strong>Finestra temporale:</strong> 60 secondi</p>
                <p><strong>Gestione automatica:</strong> Le richieste vengono automaticamente messe in coda quando si raggiunge il limite</p>

                <?php if ($api_connectivity): ?>
                <div class="mt-3">
                    <strong>Test Connettività API:</strong>
                    <?php if ($api_connectivity['status'] === 'success'): ?>
                        <span class="badge badge-success">
                            <i class="fas fa-check"></i> Connessa
                        </span>
                    <?php elseif ($api_connectivity['status'] === 'rate_limited'): ?>
                        <span class="badge badge-warning">
                            <i class="fas fa-clock"></i> Rate Limited
                        </span>
                    <?php else: ?>
                        <span class="badge badge-danger">
                            <i class="fas fa-times"></i> Errore
                        </span>
                    <?php endif; ?>
                    <br><small class="text-muted"><?= htmlspecialchars($api_connectivity['message']) ?></small>
                </div>
                <?php endif; ?>

                <?php if ($rate_limit_status['rate_limit_reached']): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Cosa fare:</strong><br>
                    - Attendi il reset del limite<br>
                    - Le richieste in coda verranno elaborate automaticamente<br>
                    - Ricarica la pagina tra qualche minuto
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php if (!empty($pending_requests)): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Richieste Pendenti (<?= count($pending_requests) ?>)</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>Coin</th>
                                <th>Data</th>
                                <th>Tipo Richiesta</th>
                                <th>Aggiunta alle</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($pending_requests as $request): ?>
                            <tr>
                                <td><code><?= htmlspecialchars($request['coin']) ?></code></td>
                                <td><?= htmlspecialchars($request['data']) ?></td>
                                <td>
                                    <span class="badge badge-secondary">
                                        <?= htmlspecialchars($request['type']) ?>
                                    </span>
                                </td>
                                <td><?= $request['timestamp'] ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="mt-3">
    <a href="<?= base_url('SnapshotWallet/lista') ?>" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Torna ai Wallet
    </a>
    <a href="<?= base_url('RateLimitStatus/config') ?>" class="btn btn-info">
        <i class="fas fa-cog"></i> Configurazione API
    </a>
    <button onclick="location.reload()" class="btn btn-primary">
        <i class="fas fa-sync-alt"></i> Aggiorna Stato
    </button>

    <?php if (!empty($pending_requests)): ?>
    <a href="<?= base_url('RateLimitStatus/processPendingRequests') ?>" class="btn btn-success">
        <i class="fas fa-play"></i> Processa Richieste Pendenti
    </a>
    <a href="<?= base_url('RateLimitStatus/cleanOldRequests') ?>" class="btn btn-warning">
        <i class="fas fa-broom"></i> Pulisci Richieste Vecchie
    </a>
    <?php endif; ?>
</div>

<?= $this->endSection() ?>

<?= $this->section('javascript') ?>
<script>
// Auto-refresh ogni 30 secondi se il rate limit è attivo
<?php if ($rate_limit_status['rate_limit_reached']): ?>
setTimeout(function() {
    location.reload();
}, 30000);
<?php endif; ?>
</script>
<?= $this->endSection() ?>
