<?php
namespace App\Models;

use CodeIgniter\Model;

class ChainModel extends Model
{
    protected $table      = 'chain';
    protected $primaryKey = 'id';

    protected $returnType = 'object';
    protected $useSoftDeletes = false;

    protected $allowedFields = ['chain'];

    protected $useTimestamps = false;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules    = [];
    protected $validationMessages = [];
    protected $skipValidation     = false;

    public function arrayopzioni()
    {
        $this->orderBy('chain', 'asc');
        $chains=$this->findAll();
        $arr=array();
        foreach ($chains as $c) {
            $arr[$c->id]=$c->chain;
        }
        return $arr;
    }
}