<?php

namespace App\Models;

use CodeIgniter\Model;
use Codenixsv\CoinGeckoApi\CoinGeckoClient;
use DateTime;

class CryptoModel extends Model
{
    /**
     * Trova valore su Coingecko della coin alla data, alle 01:00 circa
     *
     * @param string $coin (id della coin)
     * @param string $data (data nel formato dd-mm-YYYY)
     * @return void
     */
    public function getPrice($coin, $data = '')
    {
        $arr = explode('-', $data);
        // var_dump($arr);exit;
        if (strlen($arr[0]) == 4) {
            $data = $arr[2] . '-' . $arr[1] . '-' . $arr[0];
        }
        // echo $data;exit;
        $client = new CoinGeckoClient();
        // $data = $client->ping();
        // var_dump($data);

        $result = $client->coins()->getHistory($coin, $data);
        // var_dump($result);
        if (!key_exists('market_data', $result)) {
            echo "Inserire a mano il valore di $coin alla data $data";
            exit;
        } else return $result['market_data']['current_price']['usd'];
    }

    /**
     * Trova valore medio su Coingecko della coin alla data
     *
     * @param string $coin (id della coin)
     * @param string $data (data nel formato dd-mm-YYYY)
     * @return void
     */
    public function getMediaPrice($coin, $data = '')
    {
        $arr = explode('-', $data);
        // var_dump($arr);exit;
        if (strlen($arr[0]) == 4) {
            $data = $arr[2] . '-' . $arr[1] . '-' . $arr[0];
        }
        // $dataobj=new DateTime($arr[1].'-'.$arr[2].'-'.$arr[0].' 00:00:00');
        $tsinizio = strtotime($data . ' 00:00:01');
        $tsfine = strtotime($data . ' 23:59:59');

        // echo date_default_timezone_get().$tsinizio.' '.$tsfine; exit;

        // echo $data;exit;
        $client = new CoinGeckoClient();
        // $data = $client->ping();
        // var_dump($data);

        $result = $client->coins()->getMarketChartRange($coin, 'usd', $tsinizio, $tsfine);
        // var_dump($result['prices']); exit;
        if (!key_exists('prices', $result)) {
            echo "Inserire a mano il valore di $coin alla data $data";
            return 0;
        } else {
            $prezzo = 0;
            $somma = 0;
            $i = 0;
            foreach ($result['prices'] as $r) {
                $somma += $r[1];
                $i++;
            }
            ($i > 0) ? $prezzo = $somma / $i : 0;
        }

        return $prezzo;
    }
}
