<?php

namespace App\Models;

use CodeIgniter\Model;
use Codenixsv\CoinGeckoApi\CoinGeckoClient;
use DateTime;
use Exception;
use GuzzleHttp\Client;

class CryptoModel extends Model
{
    private static $requestCount = 0;
    private static $lastResetTime = null;
    private static $rateLimitReached = false;
    private static $pendingRequests = [];

    const MAX_REQUESTS_PER_MINUTE = 30;
    const RATE_LIMIT_WINDOW = 60; // seconds

    /**
     * Crea un client CoinGecko configurato con API key se disponibile
     * @return CoinGeckoClient
     */
    public function createCoinGeckoClient()
    {
        // Prova a ottenere l'API key dalle variabili d'ambiente o configurazione
        $config = config('CoinGecko');
        $apiKey = $config->apiKey ?? env('COINGECKO_API_KEY', null);

        if ($apiKey && !empty(trim($apiKey)) && $apiKey !== 'TUA_API_KEY_QUI') {
            // Determina il tipo di API key e l'URL corretto
            $isPro = strpos($apiKey, 'CG-') === 0; // Le chiavi Pro iniziano con CG-

            $baseUrl = $isPro ? 'https://pro-api.coingecko.com' : 'https://api.coingecko.com';
            $headerName = $isPro ? 'x-cg-pro-api-key' : 'x-cg-demo-api-key';

            // Log per debugging
            log_message('info', "CoinGecko API configurata: " . ($isPro ? 'Pro' : 'Demo') . " - URL: $baseUrl - Header: $headerName");

            // Crea un client Guzzle con l'header di autenticazione
            $httpClient = new Client([
                'base_uri' => $baseUrl,
                'timeout' => $config->timeout ?? 30,
                'headers' => [
                    $headerName => $apiKey,
                    'Accept' => 'application/json',
                    'User-Agent' => 'CryptoPHP/1.0'
                ]
            ]);

            return new CoinGeckoClient($httpClient);
        } else {
            // Usa il client standard senza API key (potrebbe non funzionare)
            log_message('warning', 'CoinGecko API key non configurata. Alcune richieste potrebbero fallire.');
            return new CoinGeckoClient();
        }
    }

    /**
     * Debug: testa la configurazione dell'API key
     * @return array
     */
    public function debugApiConfiguration()
    {
        $config = config('CoinGecko');
        $apiKey = $config->apiKey ?? env('COINGECKO_API_KEY', null);

        $debug = [
            'api_key_configured' => !empty($apiKey) && $apiKey !== 'TUA_API_KEY_QUI',
            'api_key_length' => $apiKey ? strlen($apiKey) : 0,
            'api_key_prefix' => $apiKey ? substr($apiKey, 0, 3) : 'N/A',
            'is_pro_key' => $apiKey ? (strpos($apiKey, 'CG-') === 0) : false,
            'base_url' => null,
            'header_name' => null,
            'test_request' => null
        ];

        if ($debug['api_key_configured']) {
            $debug['is_pro_key'] = strpos($apiKey, 'CG-') === 0;
            $debug['base_url'] = $debug['is_pro_key'] ? 'https://pro-api.coingecko.com' : 'https://api.coingecko.com';
            $debug['header_name'] = $debug['is_pro_key'] ? 'x-cg-pro-api-key' : 'x-cg-demo-api-key';

            // Test di una richiesta semplice
            try {
                $client = $this->createCoinGeckoClient();
                $result = $client->ping();
                $debug['test_request'] = [
                    'status' => 'success',
                    'response' => $result
                ];
            } catch (\Exception $e) {
                $debug['test_request'] = [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        }

        return $debug;
    }
    /**
     * Controlla e gestisce il rate limiting
     * @return bool true se la richiesta può essere effettuata, false altrimenti
     */
    private function checkRateLimit()
    {
        $currentTime = time();

        // Reset del contatore se è passato più di un minuto
        if (self::$lastResetTime === null || ($currentTime - self::$lastResetTime) >= self::RATE_LIMIT_WINDOW) {
            self::$requestCount = 0;
            self::$lastResetTime = $currentTime;
            self::$rateLimitReached = false;
        }

        // Controlla se abbiamo raggiunto il limite
        if (self::$requestCount >= self::MAX_REQUESTS_PER_MINUTE) {
            self::$rateLimitReached = true;
            return false;
        }

        return true;
    }

    /**
     * Incrementa il contatore delle richieste
     */
    private function incrementRequestCount()
    {
        self::$requestCount++;

        // Log della richiesta per debugging
        log_message('info', "CoinGecko API request #{" . self::$requestCount . "} - Remaining: " . (self::MAX_REQUESTS_PER_MINUTE - self::$requestCount));
    }

    /**
     * Aggiunge una richiesta alla coda delle richieste pendenti
     */
    private function addToPendingRequests($coin, $data, $type = 'price')
    {
        self::$pendingRequests[] = [
            'coin' => $coin,
            'data' => $data,
            'type' => $type,
            'timestamp' => time()
        ];
    }

    /**
     * Ottiene le richieste pendenti
     */
    public static function getPendingRequests()
    {
        return self::$pendingRequests;
    }

    /**
     * Ottiene lo stato del rate limiting
     */
    public static function getRateLimitStatus()
    {
        return [
            'rate_limit_reached' => self::$rateLimitReached,
            'requests_made' => self::$requestCount,
            'max_requests' => self::MAX_REQUESTS_PER_MINUTE,
            'pending_requests' => count(self::$pendingRequests),
            'reset_time' => self::$lastResetTime ? self::$lastResetTime + self::RATE_LIMIT_WINDOW : null
        ];
    }

    /**
     * Pulisce le richieste pendenti più vecchie di un certo tempo
     * @param int $maxAge Età massima in secondi (default: 1 ora)
     */
    public static function cleanOldPendingRequests($maxAge = 3600)
    {
        $currentTime = time();
        self::$pendingRequests = array_filter(self::$pendingRequests, function($request) use ($currentTime, $maxAge) {
            return ($currentTime - $request['timestamp']) < $maxAge;
        });
    }

    /**
     * Tenta di processare le richieste pendenti se il rate limit lo permette
     * @return array Risultati del processamento
     */
    public function processPendingRequests()
    {
        $results = [
            'processed' => 0,
            'failed' => 0,
            'remaining' => 0,
            'errors' => []
        ];

        // Pulisce le richieste vecchie
        self::cleanOldPendingRequests();

        if (empty(self::$pendingRequests)) {
            return $results;
        }

        // Processa le richieste se il rate limit lo permette
        $processedRequests = [];
        foreach (self::$pendingRequests as $index => $request) {
            if (!$this->checkRateLimit()) {
                break; // Rate limit raggiunto, ferma il processamento
            }

            try {
                if ($request['type'] === 'price') {
                    $this->getPrice($request['coin'], $request['data']);
                } else {
                    $this->getMediaPrice($request['coin'], $request['data']);
                }

                $processedRequests[] = $index;
                $results['processed']++;

            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = "Errore processando {$request['coin']}: " . $e->getMessage();

                // Se è un errore di rate limit, ferma il processamento
                if (strpos($e->getMessage(), 'Rate limit') !== false) {
                    break;
                }
            }
        }

        // Rimuove le richieste processate
        foreach (array_reverse($processedRequests) as $index) {
            unset(self::$pendingRequests[$index]);
        }
        self::$pendingRequests = array_values(self::$pendingRequests); // Re-index array

        $results['remaining'] = count(self::$pendingRequests);

        return $results;
    }

    /**
     * Trova valore su Coingecko della coin alla data, alle 01:00 circa
     *
     * @param string $coin (id della coin)
     * @param string $data (data nel formato dd-mm-YYYY)
     * @return float|null
     * @throws Exception
     */
    public function getPrice($coin, $data = '')
    {
        // Controlla il rate limiting
        if (!$this->checkRateLimit()) {
            $this->addToPendingRequests($coin, $data, 'price');
            throw new Exception("Rate limit CoinGecko raggiunto. Richiesta aggiunta alla coda. Riprova tra qualche minuto.");
        }

        $arr = explode('-', $data);
        if (strlen($arr[0]) == 4) {
            $data = $arr[2] . '-' . $arr[1] . '-' . $arr[0];
        }

        try {
            $client = $this->createCoinGeckoClient();
            $this->incrementRequestCount();

            $result = $client->coins()->getHistory($coin, $data);

            if (!key_exists('market_data', $result)) {
                throw new Exception("Dati non disponibili per $coin alla data $data. Inserire manualmente il valore.");
            }

            return $result['market_data']['current_price']['usd'];

        } catch (\GuzzleHttp\Exception\ClientException $e) {
            // Gestisce errori HTTP specifici
            $statusCode = $e->getResponse()->getStatusCode();

            if ($statusCode == 429) {
                // Rate limit raggiunto
                self::$rateLimitReached = true;
                $this->addToPendingRequests($coin, $data, 'price');
                throw new Exception("Rate limit CoinGecko raggiunto (429). Richiesta aggiunta alla coda. Riprova tra qualche minuto.");
            } elseif ($statusCode == 401) {
                // Errore di autorizzazione - probabilmente serve API key
                throw new Exception("Errore di autorizzazione CoinGecko (401). L'API potrebbe richiedere una chiave di accesso. Contatta l'amministratore per configurare l'API key.");
            } elseif ($statusCode == 403) {
                // Accesso negato - limiti superati o endpoint non disponibile
                throw new Exception("Accesso negato CoinGecko (403). Potresti aver superato i limiti dell'API gratuita o l'endpoint non è disponibile.");
            }

            throw new Exception("Errore API CoinGecko (HTTP $statusCode): " . $e->getMessage());

        } catch (\Exception $e) {
            throw new Exception("Errore durante il recupero del prezzo per $coin: " . $e->getMessage());
        }
    }

    /**
     * Trova valore medio su Coingecko della coin alla data
     *
     * @param string $coin (id della coin)
     * @param string $data (data nel formato dd-mm-YYYY)
     * @return float
     * @throws Exception
     */
    public function getMediaPrice($coin, $data = '')
    {
        // Controlla il rate limiting
        if (!$this->checkRateLimit()) {
            $this->addToPendingRequests($coin, $data, 'media_price');
            throw new Exception("Rate limit CoinGecko raggiunto. Richiesta aggiunta alla coda. Riprova tra qualche minuto.");
        }

        $arr = explode('-', $data);
        if (strlen($arr[0]) == 4) {
            $data = $arr[2] . '-' . $arr[1] . '-' . $arr[0];
        }

        $tsinizio = strtotime($data . ' 00:00:01');
        $tsfine = strtotime($data . ' 23:59:59');

        try {
            $client = $this->createCoinGeckoClient();
            $this->incrementRequestCount();

            $result = $client->coins()->getMarketChartRange($coin, 'usd', $tsinizio, $tsfine);

            if (!key_exists('prices', $result)) {
                throw new Exception("Dati dei prezzi non disponibili per $coin alla data $data. Inserire manualmente il valore.");
            }

            $prezzo = 0;
            $somma = 0;
            $i = 0;
            foreach ($result['prices'] as $r) {
                $somma += $r[1];
                $i++;
            }
            $prezzo = ($i > 0) ? $somma / $i : 0;

            return $prezzo;

        } catch (\GuzzleHttp\Exception\ClientException $e) {
            // Gestisce errori HTTP specifici
            $statusCode = $e->getResponse()->getStatusCode();

            if ($statusCode == 429) {
                // Rate limit raggiunto
                self::$rateLimitReached = true;
                $this->addToPendingRequests($coin, $data, 'media_price');
                throw new Exception("Rate limit CoinGecko raggiunto (429). Richiesta aggiunta alla coda. Riprova tra qualche minuto.");
            } elseif ($statusCode == 401) {
                // Errore di autorizzazione - probabilmente serve API key
                throw new Exception("Errore di autorizzazione CoinGecko (401). L'API potrebbe richiedere una chiave di accesso. Contatta l'amministratore per configurare l'API key.");
            } elseif ($statusCode == 403) {
                // Accesso negato - limiti superati o endpoint non disponibile
                throw new Exception("Accesso negato CoinGecko (403). Potresti aver superato i limiti dell'API gratuita o l'endpoint non è disponibile.");
            }

            throw new Exception("Errore API CoinGecko (HTTP $statusCode): " . $e->getMessage());

        } catch (\Exception $e) {
            throw new Exception("Errore durante il recupero del prezzo medio per $coin: " . $e->getMessage());
        }
    }

    /**
     * Verifica la connettività dell'API CoinGecko
     * @return array Stato della connessione
     */
    public function checkApiConnectivity()
    {
        try {
            if (!$this->checkRateLimit()) {
                return [
                    'status' => 'rate_limited',
                    'message' => 'Rate limit raggiunto, impossibile testare la connettività'
                ];
            }

            $client = $this->createCoinGeckoClient();
            $this->incrementRequestCount();

            $result = $client->ping();

            return [
                'status' => 'success',
                'message' => 'API CoinGecko raggiungibile',
                'data' => $result
            ];

        } catch (\GuzzleHttp\Exception\ClientException $e) {
            $statusCode = $e->getResponse()->getStatusCode();
            return [
                'status' => 'error',
                'message' => "Errore HTTP $statusCode: " . $e->getMessage(),
                'http_code' => $statusCode
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Errore di connessione: ' . $e->getMessage()
            ];
        }
    }
}
