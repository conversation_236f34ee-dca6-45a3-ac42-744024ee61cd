<?php

namespace App\Models;

use CodeIgniter\Model;

class LuoghiModel extends Model
{
    protected $table      = 'luoghi';
    protected $primaryKey = 'id';
    protected $returnType = 'object';
    protected $skipValidation     = false;
    protected $allowedFields = array('id', 'luogo', 'tipo', 'idchain');

    public function getAll()
    {
        $this->select('luoghi.*, CH.chain');
        $this->join('chain CH', 'CH.id=luoghi.idchain', 'left');
        return $this->findAll();
    }
}
