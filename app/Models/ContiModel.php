<?php

namespace App\Models;

use CodeIgniter\Model;
use App\Models\ContiCoinsModel;

class ContiModel extends Model
{
    protected $table      = 'conti';
    protected $primaryKey = 'id';
    protected $returnType = 'object';
    protected $skipValidation     = false;
    protected $allowedFields = array('idutente', 'data');
    // protected $afterFind=['afterFind'];

    // protected function afterFind(array $data)
    // {
    //     $wm = new WalletModel();
    //     foreach ($data['data'] as $k => $v) {
    //         $idconto=$v->id;
    //         $saldo = $wm->saldo($idconto);
    //         $data['data'][$k]->saldo = $saldo;
    //     }
    //     return $data;
    // }
    function getAll($idutente)
    {
        $this->select('id, data');
        $this->where('idutente', $idutente);
        $this->distinct();
        $wallet = $this->findAll();

        $wm = new WalletModel();
        $ccm = new ContiCoinsModel();
        foreach ($wallet as $k => $v) {
            $idconto = $v->id;
            $saldo = $ccm->saldo($idconto);
            $wallet[$k]->saldo = $saldo;
        }

        return $wallet;
    }
}
