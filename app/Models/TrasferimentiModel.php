<?php

namespace App\Models;

use CodeIgniter\Model;

class TrasferimentiModel extends Model
{
    protected $table      = 'trasferimenti';
    protected $primaryKey = 'id';

    protected $returnType = 'object';
    protected $useSoftDeletes = false;

    protected $allowedFields = ['data', 'idbank_da', 'idbank_a', 'qta', 'divisa', 'metodo', 'note', 'idutente'];

    protected $useTimestamps = false;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules    = [];
    protected $validationMessages = [];
    protected $skipValidation     = false;

    protected $metodi = array('Bonifico', 'Carta di credito', 'Interno');

    public function metodiToAssoc()
    {
        foreach ($this->metodi as $value) {
            $metodi[] = ['key' => $value, 'val' => $value];
        }
        return $metodi;
    }
}
