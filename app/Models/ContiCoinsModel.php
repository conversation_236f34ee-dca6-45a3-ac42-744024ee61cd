<?php

namespace App\Models;

use CodeIgniter\Model;

class ContiCoinsModel extends Model
{
    protected $table      = 'conti_coins';
    protected $primaryKey = 'id';
    protected $returnType = 'object';
    protected $skipValidation     = false;
    protected $allowedFields = array('id', 'idconto', 'idCoin', 'qta', 'idluogo', 'idwallet');
    protected $allowCallbacks = true;

    protected function afterFind(array $data)
    {
        var_dump($data);
        exit;
    }

    /**
     * Calcola il saldo del conto (utente alla data)
     *
     * @param int $idconto
     * @return double
     */
    public function saldo($idconto)
    {
        $cvm = new CoinsValoriModel();
        $cm = new ContiModel();
        $cryptomodel = new CryptoModel();

        $conto = $cm->find($idconto);
        $rs = $this->getSnapshot($idconto);
        $saldo = 0;
        foreach ($rs as $k => $coin) {
            //cerca valutazione coin alla data, se non c'è la recupera e la stora per la prossima volta
            $cvm->where(['idCoin' => $coin->idCoin, 'data' => $conto->data]);
            $cv = $cvm->findAll();
            if (count($cv) == 0 || $cv[0]->usd == 0) {
                $valore = 0;
                try {
                    $valore = $cryptomodel->getMediaPrice($coin->idCoin, $conto->data);
                } catch (\Exception $e) {
                    // Gestisce gli errori di rate limiting e altri errori API
                    $errorMessage = $e->getMessage();

                    // Imposta un messaggio flash per l'utente
                    session()->setFlashdata('err', $errorMessage);

                    // Se è un errore di rate limiting, mostra anche le informazioni sullo stato
                    if (strpos($errorMessage, 'Rate limit') !== false) {
                        $rateLimitStatus = \App\Models\CryptoModel::getRateLimitStatus();
                        $pendingRequests = \App\Models\CryptoModel::getPendingRequests();

                        $additionalInfo = "Richieste effettuate: {$rateLimitStatus['requests_made']}/{$rateLimitStatus['max_requests']}. ";
                        $additionalInfo .= "Richieste in coda: " . count($pendingRequests) . ". ";

                        if ($rateLimitStatus['reset_time']) {
                            $resetTime = date('H:i:s', $rateLimitStatus['reset_time']);
                            $additionalInfo .= "Limite si resetta alle: $resetTime.";
                        }

                        session()->setFlashdata('msg', $additionalInfo);
                        session()->setFlashdata('msgtype', 'warning');
                    }

                    // Log dell'errore per debugging
                    log_message('error', "CoinGecko API Error for {$coin->idCoin}: " . $errorMessage);
                }

                if (count($cv) == 0) {
                    $cvm->insert(['idCoin' => $coin->idCoin, 'data' => $conto->data, 'usd' => $valore]);
                }
                else {
                    $cvm->update($cv[0]->id, ['idCoin' => $coin->idCoin, 'data' => $conto->data, 'usd' => $valore]);
                }
            } else {
                $valore = $cv[0]->usd;
            }
            // +totale in $
            $saldo += $valore * $coin->qta;
        }
        return $saldo;
    }
    /**
     * Undocumented function
     *
     * @param [type] $idconto
     * @return void
     */
    public function getSnapshot($idconto)
    {
        $this->select('conti_coins.*,  CH.chain, L.luogo, L.tipo, C.symbol, C.name, (CV.usd*conti_coins.qta) AS valore, WA.id as idwallet, CV.usd as coinprice, CV.id as id_coin_valore');
        // $this->table('conti_coins CC');
        $this->join('coins C', 'C.id=conti_coins.idCoin');
        $this->join('luoghi L', 'L.id=conti_coins.idLuogo', 'left');
        $this->join('conti CO', 'CO.id=conti_coins.idconto');
        $this->join('coins_valori CV', 'CV.idCoin=conti_coins.idCoin AND CV.`data`=CO.`data`', 'left');
        $this->join('wallet WA', 'WA.id=conti_coins.idwallet', 'left');
        $this->join('chain CH', 'CH.id=L.idchain', 'left');
        $this->where('conti_coins.idconto', $idconto);
        $this->orderby('symbol');
        // echo $this->builder->getCompiledSelect();
        // exit;
        return $this->findAll();
    }

    public function getSnapshotAggregato($idconto)
    {
        $this->select('conti_coins.idCoin, 0 as idluogo, "" as luogo, "" as chain, sum(conti_coins.qta) as qta, C.symbol, (CV.usd*sum(conti_coins.qta)) AS valore, CV.usd as coinprice, CV.id as id_coin_valore');
        // $this->table('conti_coins AS CC');
        $this->join('coins C', 'C.id=conti_coins.idCoin');
        $this->join('conti CO', 'CO.id=conti_coins.idconto');
        $this->join('coins_valori CV', 'CV.idCoin=conti_coins.idCoin AND CV.`data`=CO.`data`', 'left');
        // $this->join('luoghi L', 'L.id=idLuogo', 'left');
        $this->where('conti_coins.idconto', $idconto);
        $this->groupby('conti_coins.idCoin, C.symbol, CV.usd');
        $this->orderby('C.symbol');
        // echo $this->getCompiledSelect();exit;
        $rows = $this->findAll();

        return $rows;
    }
}
