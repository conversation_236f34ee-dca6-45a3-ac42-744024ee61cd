<?php

namespace App\Models;

use CodeIgniter\Model;

class BankModel extends Model
{
    protected $table      = 'bank';
    protected $primaryKey = 'id';

    protected $returnType = 'object';
    protected $useSoftDeletes = false;

    protected $allowedFields = ['nome', 'tipo'];

    protected $useTimestamps = false;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules    = [];
    protected $validationMessages = [];
    protected $skipValidation     = false;

    protected $tipi = ['Banca', 'Exchange', 'Card'];

    public function tipiToAssoc()
    {
        foreach ($this->tipi as $value) {
            $tipi[] = ['key' => $value, 'val' => $value];
        }
        return $tipi;
    }
}
