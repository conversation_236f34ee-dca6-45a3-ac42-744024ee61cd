<?php

/**
 * Crea input text con label e classe form-control
 *
 * @param string $label
 * @param string $name
 * @param string $value
 * @param string $extraclass classi aggiuntive
 * @param string $extraclass attrinbuti aggiuntivi
 * @return string codice html
 */
function chrishtml_input($label, $name, $value = '', $extraclass = '', $attr = '')
{
    $html = '';
    $html .= '
        <div class="form-group">
            <label for="' . $name . '">' . $label . '</label>
            <input type="text" ' . $attr . ' class="form-control ' . $extraclass . '" id="' . $name . '" name="' . $name . '" value="' . $value . '">
        </div>
    ';
    return $html;
}

/**
 * Crea input text con calendario
 *
 * @param string $label
 * @param string $name
 * @param string $value
 * @param int $cols Colonne bootstrap
 * @return string codice html
 */
function chrishtml_calendar($label, $name, $value = '')
{
    $html = '';
    $html .= '
        <div class="form-group">
            <label for="' . $name . '">' . $label . '</label>
            <input type="text" class="form-control daterange-btn" id="' . $name . '" name="' . $name . '" value="' . $value . '">
        </div>
    ';
    return $html;
}

/**
 * Crea select
 *
 * @param string $label
 * @param string $name
 * @param array $options array ([key=>key, val=>val])
 * @param string $selectedvalue valore selezionato del campo
 * @param int $id di della select
 * @return string
 */
function chrishtml_dropdown($label, $name, $options, $selectedvalue = '', $id = '')
{

    $op = '';
    $html = '';
    foreach ($options as $option) {
        $selected = ($option['key'] == $selectedvalue) ? 'selected' : '';
        $op .= '<option ' . $selected . ' value="' . $option['key'] . '">' . $option['val'] . '</option>';
    }
    $html .= '
        <div class="form-group">
            <label for="' . $name . '">' . $label . '</label>
            <select id="' . (($id != '') ? $id : $name) . '" name="' . $name . '" class="form-control">
                ' . $op . '
            </select>
        </div>
    ';

    return $html;
}
