<?php

/**
 * Array('v1', 'v2'...) -> Array('v1'=>'v1', 'v2'=>'v2'...)
 *
 * @param [type] $array
 * @return void
 */
function arrayToAssoc($array)
{
    $out = array();
    foreach ($array as $value) {
        $out[] = ['key' => $value, 'val' => $value];
    }
    return $out;
}

/**
 * Array(obj{id, name, ..}, obj{id, name, ..}...) -> Array('keyname'=>'valname', 'keyname'=>'valname'...)
 *
 * @param array     $array   of oby
 * @param string    $keyname nome della proprietà chiave
 * @param string    $valname nome della proprietà valore
 * @param array     $primo  Prima selezione o vuota [''=>'Seleziona...']
 * @param int       $substr Se visualizzare substr inizio e fine del valore
 * @return void
 */
function arrayObjToAssoc($array, $keyname, $valname, $primo = ['' => ''], $substr = 0)
{
    $out = $primo;
    foreach ($array as $value) {
        if ($substr > 0) {
            $value->{$valname} = substr($value->{$valname}, 0, 5) . '...' . substr($value->{$valname}, -5);
        }
        $out[$value->{$keyname}] = $value->{$valname};
    }
    return $out;
}
