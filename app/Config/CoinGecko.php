<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;

class CoinGecko extends BaseConfig
{
    /**
     * CoinGecko API Key
     * 
     * Ottieni la tua API key gratuita da: https://www.coingecko.com/en/api/pricing
     * 
     * @var string|null
     */
    public $apiKey = null;
    
    /**
     * Base URL dell'API CoinGecko
     * 
     * @var string
     */
    public $baseUrl = 'https://api.coingecko.com';
    
    /**
     * Rate limiting - richieste per minuto
     * 
     * @var int
     */
    public $maxRequestsPerMinute = 30;
    
    /**
     * Timeout per le richieste HTTP (in secondi)
     * 
     * @var int
     */
    public $timeout = 30;
    
    public function __construct()
    {
        parent::__construct();
        
        // Prova a ottenere l'API key dalle variabili d'ambiente
        $this->apiKey = env('COINGECKO_API_KEY', $this->apiKey);
    }
}
